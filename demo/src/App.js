// App.jsx
import React, { useEffect, useState } from "react";

export default function App() {
  const [episodes, setEpisodes] = useState([]);
  const [currentAudio, setCurrentAudio] = useState(null);

  useEffect(() => {
    fetch("http://localhost:4000/feed")
      .then(res => res.json())
      .then(data => setEpisodes(data));
  }, []);

  return (
    <div style={{ padding: "20px" }}>
      <h1>Up First (NPR)</h1>
      {episodes.map((ep, idx) => (
        <div key={idx} style={{ marginBottom: "15px" }}>
          <h3>{ep.title}</h3>
          <p>{ep.pubDate}</p>
          <button onClick={() => setCurrentAudio(ep.audio)}>Play</button>
        </div>
      ))}
      {currentAudio && (
        <audio controls autoPlay src={currentAudio} style={{ width: "100%", marginTop: "20px" }} />
      )}
    </div>
  );
}
