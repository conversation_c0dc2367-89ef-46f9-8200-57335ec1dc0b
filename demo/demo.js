// server.js
import express from "express";
import Parser from "rss-parser";
import cors from "cors";

const app = express();
const parser = new Parser();
app.use(cors());

app.get("/feed", async (req, res) => {
  try {
    const feed = await parser.parseURL("https://feeds.npr.org/510318/podcast.xml");
    const episodes = feed.items.map(item => ({
      title: item.title,
      audio: item.enclosure?.url,
      pubDate: item.pubDate,
      description: item.contentSnippet
    }));
    res.json(episodes);
  } catch (error) {
    console.error(error);
    res.status(500).send("Failed to fetch RSS");
  }
});

app.listen(4000, () => console.log("Server running on port 4000"));
